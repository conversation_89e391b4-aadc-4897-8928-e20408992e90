import 'package:dio/dio.dart';
import 'package:logger/logger.dart';
import 'package:sba/src/api/interceptors/logging_interceptor.dart';
import 'package:sba/src/api/interceptors/token_interceptor.dart';
import 'package:sba/src/repository/auth/auth_repository.dart';

const _tag = 'HttpClient';

Dio buildClient({required Logger logger, required String baseUrl}) => Dio(
  BaseOptions(
    baseUrl: baseUrl,
    connectTimeout: const Duration(seconds: 10),
    receiveTimeout: const Duration(seconds: 15),
    sendTimeout: const Duration(seconds: 10),
    headers: {Headers.contentTypeHeader: Headers.jsonContentType},
  ),
)..interceptors.add(LoggerInterceptor(logger: logger, tag: _tag));

Dio buildAuthenticatedClient({
  required Logger logger,
  required String baseUrl,
  required AuthRepository auth,
}) {
  final dio = buildClient(logger: logger, baseUrl: baseUrl);
  dio.interceptors.add(TokenInterceptor(repository: auth, client: dio));
  return dio;
}
