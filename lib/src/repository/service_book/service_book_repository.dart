import 'package:logger/logger.dart';
import 'package:memory_cache/memory_cache.dart';
import 'package:sba/src/api/service_book_api.dart';
import 'package:sba/src/common/result/call_with_result.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/common/result/result_extension.dart';
import 'package:sba/src/repository/general/general_repository.dart';
import 'package:sba/src/repository/service_book/model/annual_inspection_data.dart';
import 'package:sba/src/repository/service_book/model/engine_oil_data.dart';
import 'package:sba/src/repository/service_book/model/refuel_data.dart';
import 'package:sba/src/repository/service_book/model/transmission_oil_data.dart';
import 'package:sba/src/repository/service_book/model/tyre_data.dart';
import 'package:sba/src/repository/service_book/model/tyre_swap_data.dart';
import 'package:sba/src/repository/service_book/model/vehicle_service_data.dart';

const _tag = 'ServiceBookRepository';
const _engineOilKey = 'engine_oil';
const _transmissionOilKey = 'transmission_oil';
const _servicesKey = 'services';
const _tiresKey = 'tires';
const _swapTiresKey = 'swap_tires';
const _refuelingKey = 'refuel';
const _annualInspectionKey = 'annualInspection';

final class ServiceBookRepository {
  ServiceBookRepository({
    required ServiceBookApi api,
    required GeneralRepository generalRepository,
    required Logger logger,
  }) : _api = api,
       _generalRepository = generalRepository,
       _logger = logger;

  final ServiceBookApi _api;
  final GeneralRepository _generalRepository;
  final Logger _logger;

  Future<Result<List<EngineOilData>>> getEngineOilForVehicle({
    required int vehicleId,
  }) async {
    try {
      final result = await callWithCachedResult(
        () => _api.getEngineOilChanges(vehicleId: vehicleId),
        _logger,
        cacheKey: _keyForVehicle(key: _engineOilKey, vehicleId: vehicleId),
      );

      return result.listMap(EngineOilData.fromDto);
    } catch (e) {
      _logger.e(_tag, error: e);
      return Result.otherError(e);
    }
  }

  Future<Result<void>> crudEngineOil(
    EngineOilData data, {
    bool delete = false,
  }) async {
    try {
      final result = callWithResult(
        () => data.id == null
            ? _api.addEngineOilChange(data: data.toDto())
            : delete
            ? _api.deleteEngineOilChange(id: data.id!)
            : _api.updateEngineOilChange(data: data.toDto()),
        _logger,
      );

      MemoryCache.instance.delete(
        _keyForVehicle(key: _engineOilKey, vehicleId: data.vehicleId),
      );

      return result;
    } catch (e) {
      _logger.e(_tag, error: e);
      return Result.otherError(e);
    }
  }

  Future<Result<List<TransmissionOilData>>> getTransmissionOilForVehicle({
    required int vehicleId,
  }) async {
    try {
      final result = await callWithCachedResult(
        () => _api.getTransmissionOil(vehicleId: vehicleId),
        _logger,
        cacheKey: _keyForVehicle(
          key: _transmissionOilKey,
          vehicleId: vehicleId,
        ),
      );

      return result.listMap(TransmissionOilData.fromDto);
    } catch (e) {
      _logger.e(_tag, error: e);
      return Result.otherError(e);
    }
  }

  Future<Result<void>> crudTransmissionOil(
    TransmissionOilData data, {
    bool delete = false,
  }) async {
    try {
      final result = callWithResult(
        () => data.id == null
            ? _api.addTransmissionOilChange(data: data.toDto())
            : delete
            ? _api.deleteTransmissionOilChange(id: data.id!)
            : _api.updateTransmissionOilChange(data: data.toDto()),
        _logger,
      );

      MemoryCache.instance.delete(
        _keyForVehicle(key: _engineOilKey, vehicleId: data.vehicleId),
      );

      return result;
    } catch (e) {
      _logger.e(_tag, error: e);
      return Result.otherError(e);
    }
  }

  Future<Result<List<VehicleServiceData>>> getServicesForVehicle({
    required int vehicleId,
  }) async {
    try {
      final result = await callWithCachedResult(
        () => _api.getVehicleServices(vehicleId: vehicleId),
        _logger,
        cacheKey: _keyForVehicle(key: _servicesKey, vehicleId: vehicleId),
      );

      return result.listMap(VehicleServiceData.fromDto);
    } catch (e) {
      _logger.e(_tag, error: e);
      return Result.otherError(e);
    }
  }

  Future<Result<void>> crudVehicleService(
    VehicleServiceData data, {
    bool delete = false,
  }) async {
    try {
      final result = callWithResult(
        () => data.id == null
            ? _api.addVehicleService(data: data.toDto())
            : delete
            ? _api.deleteVehicleService(id: data.id!)
            : _api.updateVehicleService(data: data.toDto()),
        _logger,
      );

      MemoryCache.instance.delete(
        _keyForVehicle(
          key: _servicesKey,
          vehicleId: data.vehicleId,
        ),
      );

      return result;
    } catch (e) {
      _logger.e(_tag, error: e);
      return Result.otherError(e);
    }
  }

  Future<Result<List<TyreData>>> getTyres({
    required int vehicleId,
  }) async {
    try {
      final result = await callWithCachedResult(
        () => _api.getTyres(vehicleId: vehicleId),
        _logger,
        cacheKey: _keyForVehicle(key: _tiresKey, vehicleId: vehicleId),
      );

      final brands = result.maybeValue?.isNotEmpty ?? false
          ? await _generalRepository.getTyres().then((e) => e.maybeValue)
          : null;

      return result.listMap((e) => TyreData.fromData(dto: e, brands: brands));
    } catch (e) {
      _logger.e(_tag, error: e);
      return Result.otherError(e);
    }
  }

  Future<Result<void>> crudTyres(
    TyreData data, {
    bool delete = false,
  }) async {
    try {
      final result = callWithResult(
        () => data.id == null
            ? _api.addTyres(data: data.toDto())
            : delete
            ? _api.deleteTyres(id: data.id!)
            : _api.updateTyres(data: data.toDto()),
        _logger,
      );

      MemoryCache.instance.delete(
        _keyForVehicle(key: _tiresKey, vehicleId: data.vehicleId),
      );

      return result;
    } catch (e) {
      _logger.e(_tag, error: e);
      return Result.otherError(e);
    }
  }

  Future<Result<List<TyreSwapData>>> getTyreSwaps({
    required int vehicleId,
  }) async {
    try {
      final result = await callWithCachedResult(
        () => _api.getTyreSwitches(vehicleId: vehicleId),
        _logger,
        cacheKey: _keyForVehicle(key: _swapTiresKey, vehicleId: vehicleId),
      );

      final tyres = result.maybeValue?.isNotEmpty ?? false
          ? await getTyres(vehicleId: vehicleId).then((e) => e.maybeValue)
          : null;

      return result.listMap((e) => TyreSwapData.fromData(dto: e, tyres: tyres));
    } catch (e) {
      _logger.e(_tag, error: e);
      return Result.otherError(e);
    }
  }

  Future<Result<void>> crudTyreSwap(
    TyreSwapData data, {
    bool delete = false,
  }) async {
    try {
      final result = callWithResult(
        () => data.id == null
            ? _api.addTyreSwitch(data: data.toDto())
            : delete
            ? _api.deleteTyreSwitch(id: data.id!)
            : _api.updateTyreSwitch(data: data.toDto()),
        _logger,
      );

      MemoryCache.instance.delete(
        _keyForVehicle(key: _swapTiresKey, vehicleId: data.vehicleId),
      );

      return result;
    } catch (e) {
      _logger.e(_tag, error: e);
      return Result.otherError(e);
    }
  }

  Future<Result<List<RefuelData>>> getRefuels({
    required int vehicleId,
  }) async {
    try {
      final result = await callWithCachedResult(
        () => _api.getRefuelings(vehicleId: vehicleId),
        _logger,
        cacheKey: _keyForVehicle(key: _refuelingKey, vehicleId: vehicleId),
      );

      final traders = result.maybeValue?.isNotEmpty ?? false
          ? await _generalRepository.getFuels().then((e) => e.maybeValue)
          : null;

      return result.listMap(
        (e) => RefuelData.fromData(dto: e, traders: traders),
      );
    } catch (e) {
      _logger.e(_tag, error: e);
      return Result.otherError(e);
    }
  }

  Future<Result<void>> crudRefuel(
    RefuelData data, {
    bool delete = false,
  }) async {
    try {
      final result = callWithResult(
        () => data.id == null
            ? _api.addRefueling(data: data.toDto())
            : delete
            ? _api.deleteRefueling(id: data.id!)
            : _api.updateRefueling(data: data.toDto()),
        _logger,
      );

      MemoryCache.instance.delete(
        _keyForVehicle(key: _refuelingKey, vehicleId: data.vehicleId),
      );

      return result;
    } catch (e) {
      _logger.e(_tag, error: e);
      return Result.otherError(e);
    }
  }

  Future<Result<List<AnnualInspectionData>>> getAnnualInspectionForVehicle({
    required int vehicleId,
  }) async {
    try {
      final result = await callWithCachedResult(
        () => _api.getAnnualInspection(vehicleId: vehicleId),
        _logger,
        cacheKey: _keyForVehicle(
          key: _annualInspectionKey,
          vehicleId: vehicleId,
        ),
      );

      return result.listMap(AnnualInspectionData.fromDto);
    } catch (e) {
      _logger.e(_tag, error: e);
      return Result.otherError(e);
    }
  }

  Future<Result<void>> crudAnnualInspection(
    AnnualInspectionData data, {
    bool delete = false,
  }) async {
    try {
      final result = callWithResult(
        () => data.id == null
            ? _api.addAnnualInspection(data: data.toDto())
            : delete
            ? _api.deleteAnnualInspection(id: data.id!)
            : _api.updateAnnualInspection(data: data.toDto()),
        _logger,
      );

      MemoryCache.instance.delete(
        _keyForVehicle(key: _annualInspectionKey, vehicleId: data.vehicleId),
      );

      return result;
    } catch (e) {
      _logger.e(_tag, error: e);
      return Result.otherError(e);
    }
  }

  String _keyForVehicle({required String key, int? vehicleId}) =>
      '${key}_$vehicleId';
}
